[{"description": "", "elements": [{"description": "", "id": "upload;verify-user-can-close-the-upload-screen", "keyword": "<PERSON><PERSON><PERSON>", "line": 82, "name": "Verify user can close the Upload screen", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 25482000000}}, {"arguments": [{"rows": [{"cells": ["filename"]}, {"cells": ["e2e/upload/image-plant-jpeg.jpeg"]}, {"cells": ["e2e/upload/spanish-pdf.pdf"]}, {"cells": ["e2e/upload/spanish-email.eml"]}, {"cells": ["e2e/upload/spanish-txt.txt"]}]}], "keyword": "Given ", "line": 4, "name": "Navigate to upload folder and delete file:", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/uploadFile/uploadFile.steps.ts:13"}, "result": {"status": "passed", "duration": 4413000000}}, {"arguments": [], "keyword": "When ", "line": 83, "name": "The user closes the upload screen", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/uploadFile/uploadFile.steps.ts:101"}, "result": {"status": "passed", "duration": 756000000}}], "tags": [{"name": "@e2e", "line": 81}, {"name": "@upload", "line": 81}, {"name": "@closeUploadScreen", "line": 81}], "type": "scenario"}], "id": "upload", "line": 1, "keyword": "Feature", "name": "Upload", "tags": [], "uri": "cypress\\e2e\\features\\uploadFile.feature"}]