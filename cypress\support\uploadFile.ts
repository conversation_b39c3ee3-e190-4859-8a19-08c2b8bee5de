import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTranslate,
  engineIdAmazonTranslate,
} from '../fixtures/variables';

Cypress.Commands.add('OpenModalUploadFile', () => {
  cy.get('[data-test="upload-file"]').click();
  cy.get('[data-test="upload-media"]').click();
});

Cypress.Commands.add(
  'SelectFile',
  (fileName: string, videoPath: string, mimeType: string) => {
    cy.fixture(videoPath, 'binary', { timeout: 120000 })
      .then(Cypress.Blob.binaryStringToBlob)
      .then((fileContent) => {
        console.log('fileContent', fileContent);
        cy.get('input[type="file"]').attachFile({
          fileContent,
          fileName: fileName,
          mimeType: mimeType,
          encoding: 'utf8',
        });
        return;
      });
  }
);

Cypress.Commands.add('ClickToUpload', () => {
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
});

Cypress.Commands.add('AddTagsUpload', () => {
  cy.get('[data-test="tags-upload"] input').type('demo{enter}');
});

Cypress.Commands.add('EditFile', (fileName: string) => {
  cy.get('[data-test="checked-all-file"]').find('[type="checkbox"]').check();
  cy.get('[data-test="edit-file"]').click();
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type(fileName);
  cy.get('[data-testid="tags-edit"]').click();
  cy.AddTagsUpload();
  cy.contains('General').click();
  cy.get('[data-test="save-edit-file"]').click();
});

Cypress.Commands.add('FileUpload', (fileName, videoPath, mimeType) => {
  cy.OpenModalUploadFile();
  cy.SelectFile(fileName, videoPath, mimeType);
  cy.ClickToUpload();
  cy.EditFile(fileName);
});

Cypress.Commands.add('NextStep', () => {
  cy.get('[data-test="next-step"]').click();
});

Cypress.Commands.add(
  'ClickEngineCategory',
  (categoryIdTranscription: string) => {
    cy.get(
      `[data-test="click-engine-category_${categoryIdTranscription}"]`
    ).click();
  }
);

Cypress.Commands.add(
  'SelectAvailableEngine',
  (categoryId: string, engineId: string) => {
    cy.get('[data-test="select-available-engine"]').click();
    cy.get(`[data-test="list-available-engine_${categoryId}"]`).click();
    cy.get(`[data-test="add-engine-available_${engineId}"]`).click();
  }
);

Cypress.Commands.add('SaveTemplateEngine', () => {
  cy.get('[data-test="show-modal-save-template-engine"]').click();
  cy.get('[data-test="template-engine-name"] input').type(
    'test template engine'
  );
  cy.get('[data-test="save-template-engine"]').click();
});

Cypress.Commands.add('AddContentTemplate', () => {
  cy.get('[data-test="list-content-template"]').each(($el) => {
    if ($el.text() === 'Override') {
      cy.get(
        '[data-test="add-content-template_8c61fc4c-953f-4be8-b091-88e4069a9106"]'
      ).click();
      cy.get('[data-test="age"]').type('age');
      cy.get('[data-test="country"]').type('country');
      cy.get('[data-test="filename"]').type('filename');
    }
  });
});

Cypress.Commands.add('ClickSimpleCognitiveWorkflow', () => {
  cy.get('body').then(($body) => {
    if ($body.find('[data-test="show-simple-cognitive-workflow"]').length > 0) {
      // show simple cognitive workflow
      cy.get('[data-test="show-simple-cognitive-workflow"]').click();
    }
    return;
  });
});

Cypress.Commands.add('SelectFolder', () => {
  cy.get('[data-test="select-folder"]').click();
  cy.get('ul button')
    .filter((_index, element) => Cypress.$(element).text() === 'e2e')
    .within(() => {
      return cy.get('[data-testid="arrow-right-img"]').click();
    });
  cy.get('[data-test="list-folder"]').contains('upload').click();
  cy.get('[data-test=move-folder-submit-button]').click();
});

Cypress.Commands.add(
  'SelectLanguages',
  (sourceLanguage: string, targetLanguage: string) => {
    cy.get('[data-test="fields-engine_sourceLanguageCode"]').click();
    cy.get(`[data-test="${sourceLanguage}"]`).click();
    cy.get('[data-test="fields-engine_target"]').click();
    cy.get(`[data-test="${targetLanguage}"]`).click();
  }
);

Cypress.Commands.add(
  'UploadFileBasic',
  (
    fileName: string,
    filePath: string,
    mimeType: string,
    categoryId: string,
    engineId: string
  ) => {
    // upload file
    cy.FileUpload(fileName, filePath, mimeType);
    cy.NextStep();
    // use advanced mode
    cy.contains('Show Advanced Cognitive Workflow').click();
    // test select category
    cy.SelectAvailableEngine(categoryId, engineId);
    cy.NextStep();
    // next step content template
    cy.NextStep();
    // select folder e2e/upload
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileAdvanced',
  (fileName: string, filePath: string, mimeType: string, categoryEngines) => {
    // upload file
    cy.FileUpload(fileName, filePath, mimeType);
    // next step processing
    cy.NextStep();
    // use advanced mode
    cy.contains('Show Advanced Cognitive Workflow').click();
    // test select category
    categoryEngines.forEach(
      (categoryEngine: { categoryId: string; engineId: string }) => {
        cy.SelectAvailableEngine(
          categoryEngine.categoryId,
          categoryEngine.engineId
        );
      }
    );
    // save template engine
    cy.SaveTemplateEngine();

    cy.get('button').each(($el) => {
      if ($el.text() === 'Override') {
        cy.get('[data-test="confirm-button"]').click();
      }
    });
    // next step content template
    cy.NextStep();
    cy.AddContentTemplate();
    // next step customize
    cy.NextStep();
    // select folder e2e test
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileImageWithEngineFacialDetection',
  (
    fileName: string,
    videoPath: string,
    mimeType: string,
    categoryIdFacialDetection: string,
    engineIdFaceboxSimilarity: string
  ) => {
    // upload file
    cy.FileUpload(fileName, videoPath, mimeType);
    // next step processing
    cy.NextStep();
    // check mode simple
    cy.ClickSimpleCognitiveWorkflow();
    // test select category
    cy.ClickEngineCategory(categoryIdFacialDetection);
    // test unselect category
    cy.ClickEngineCategory(categoryIdFacialDetection);
    // show advenced cognitive workflow
    cy.get('[data-test="show-advanced-cognitive-workflow"]').click();
    // test select category translate
    cy.SelectAvailableEngine(
      categoryIdFacialDetection,
      engineIdFaceboxSimilarity
    );
    // select libraries
    cy.get('[data-test="select-libraries"]').click();
    cy.get('[data-test="list-libraries_California Known Offenders"]').click();
    // next step content template
    cy.NextStep();
    cy.AddContentTemplate();
    // next step customize
    cy.NextStep();
    // select folder e2e test
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
  }
);

Cypress.Commands.add(
  'ReprocessBasic',
  (categoryId: string, engineId: string, fileName: string) => {
    cy.contains('FILES').click();
    cy.contains(fileName).parent().prev().click();
    cy.get('[data-test="reprocess-file-icon-button"]').click();
    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.SelectAvailableEngine(categoryId, engineId);
    cy.get('[data-test="save-reprocess"]').click();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'ReprocessAllWithEngineTranscription',
  (categoryIdTranscription) => {
    cy.get('[data-test="files-table-row"]').find('[type="checkbox"]').check();
    cy.get('[data-test="reprocess-file"]').click();
    cy.ClickEngineCategory(categoryIdTranscription);
    cy.get('[data-test="save-reprocess"]').click();
  }
);

Cypress.Commands.add(
  'ReprocessFileVideoWithEngineAmazonTranslate',
  (categoryIdTranslate, engineIdTranslate) => {
    // cy.get('[data-testid^=files-table-row]')
    //   .find('[type="checkbox"]')
    //   .first()
    //   .check();
    cy.get('[data-test="reprocess-file-icon-button"]').click();
    cy.SelectAvailableEngine(categoryIdTranslate, engineIdTranslate);
    cy.SelectLanguages('sw', 'en');
    cy.get('[data-test="save-reprocess"]').click();
    cy.contains('1 jobs are created successfully');
  }
);

Cypress.Commands.add(
  'UploadMultipleFilesBasic',
  (
    files: Array<{
      fileName: string;
      filePath: string;
      mimeType: string;
      categoryId: string;
      engineId: string;
    }>
  ) => {
    if (files.length < 2) {
      throw new Error(
        'At least 2 files are required for multiple file upload test'
      );
    }

    cy.get('[data-test="upload-file"]').click();
    cy.get('[data-test="upload-media"]').click();

    cy.get('input[type="file"]').attachFile([
      {
        filePath: files[0]!.filePath,
        fileName: files[0]!.fileName,
        mimeType: files[0]!.mimeType,
      },
      {
        filePath: files[1]!.filePath,
        fileName: files[1]!.fileName,
        mimeType: files[1]!.mimeType,
      },
    ]);

    cy.get('[data-veritone-element="picker-footer-Upload-button"]')
      .should('be.enabled')
      .click();

    cy.get('[data-testid="stepper"]').should('be.visible');
    cy.contains('Processing').should('be.visible');
    cy.NextStep();
    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.SelectAvailableEngine(files[0]!.categoryId, files[0]!.engineId);
    cy.NextStep();
    cy.NextStep();
    cy.SelectFolder();
    cy.AddTagsUpload();
    cy.NextStep();
    cy.contains('created successfully');
  }
);

// TODO: Implement proper drag and drop upload command
// This should use real drag and drop events like:
// - cy.trigger('dragenter', { dataTransfer })
// - cy.trigger('dragover', { dataTransfer })
// - cy.trigger('drop', { dataTransfer })
// Instead of using attachFile which simulates browse functionality
//
// Cypress.Commands.add(
//   'UploadFileDragAndDrop',
//   (
//     fileName: string,
//     filePath: string,
//     mimeType: string,
//     categoryId: string,
//     engineId: string
//   ) => {
//     // Implementation needed for actual drag and drop functionality
//   }
// );

Cypress.Commands.add(
  'UploadFileByUrl',
  (imageUrl: string, categoryId: string, engineId: string) => {
    // Open upload modal
    cy.OpenModalUploadFile();

    // Switch to "By URL" tab
    cy.get('[data-testid="file-picker-url-tab"]').click();

    // Enter URL in the input field by simulating paste event
    cy.get('#url-input').should('be.visible');
    cy.get('#url-input').focus();
    cy.get('#url-input').trigger('paste', {
      clipboardData: {
        getData: () => imageUrl,
      },
    });

    // Wait for image to load and display
    cy.get('img[alt="Uploaded content"]', { timeout: 20000 }).should(
      'be.visible'
    );

    // Click upload button
    cy.ClickToUpload();

    // Continue with the upload process similar to other upload tests
    cy.NextStep();
    // use advanced mode
    cy.contains('Show Advanced Cognitive Workflow').click();
    // test select category
    cy.SelectAvailableEngine(categoryId, engineId);
    cy.NextStep();
    // next step content template
    cy.NextStep();
    // select folder e2e/upload
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add('AddMoreFilesWithPlusIcon', () => {
  // First upload an initial file to get to the file list screen
  cy.OpenModalUploadFile();

  const fileName = 'image-plant-jpeg.jpeg';
  const filePath = '../setup/image-plant-jpeg.jpeg';
  const mimeType = 'image/jpeg';

  cy.SelectFile(fileName, filePath, mimeType);
  cy.ClickToUpload();

  // Now we should be on the screen with the + icon
  // Wait for the file list to appear and verify we have 1 file
  cy.contains('1 files').should('be.visible');

  // Click the + icon to add more files
  cy.get('[data-testid="add-file"]').should('be.visible').click();

  // This should open the file picker again
  // Upload a second file
  const secondFileName = 'e2e_audio.mp3';
  const secondFilePath = '../setup/e2e_audio.mp3';
  const secondMimeType = 'audio/mp3';

  cy.SelectFile(secondFileName, secondFilePath, secondMimeType);
  cy.ClickToUpload();

  // Verify that we now have multiple files
  cy.contains('2 files').should('be.visible');

  // Edit the files to complete the upload process
  cy.get('[data-test="checked-all-file"]').find('[type="checkbox"]').check();
  cy.get('[data-test="edit-file"]').click();
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type('test-multiple-files');
  cy.get('[data-testid="tags-edit"]').click();
  cy.AddTagsUpload();
  cy.contains('General').click();
  cy.get('[data-test="save-edit-file"]').click();

  // Continue with the upload process
  cy.NextStep();
  // use advanced mode
  cy.contains('Show Advanced Cognitive Workflow').click();
  // test select category for the files
  cy.SelectAvailableEngine(categoryIdObjectDetection, engineIdMachineboxTagbox);
  cy.NextStep();
  // next step content template
  cy.NextStep();
  // select folder e2e/upload
  cy.SelectFolder();
  // add tags upload
  cy.AddTagsUpload();
  // save upload file
  cy.NextStep();
  cy.contains('created successfully');
});

Cypress.Commands.add('EditSelectedFileInUploadScreen', () => {
  // First upload a file to get to the file list screen
  cy.OpenModalUploadFile();

  const fileName = 'image-plant-jpeg.jpeg';
  const filePath = '../setup/image-plant-jpeg.jpeg';
  const mimeType = 'image/jpeg';

  cy.SelectFile(fileName, filePath, mimeType);
  cy.ClickToUpload();

  // Wait for the file list to appear - look for the file name or file count
  cy.get('[data-testid="list-file"]', { timeout: 15000 }).should('be.visible');

  // Select the file by checking the checkbox
  cy.get('[data-testid="checkbox-file"]').should('be.visible').click();

  // Verify the edit button is now enabled and click it
  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  // Verify the edit dialog opens
  cy.contains('Edit Media').should('be.visible');

  // Edit the file name
  const newFileName = 'edited-test-file';
  cy.get('[data-test="file-name-upload"] input').should('be.visible').clear();
  cy.get('[data-test="file-name-upload"] input').type(newFileName);

  // Add tags
  cy.get('[data-testid="tags-edit"]').click();
  cy.AddTagsUpload();

  // Select category
  cy.contains('General').click();

  // Save the edit
  cy.get('[data-test="save-edit-file"]').click();

  // Verify we're back to the file list
  cy.get('[data-testid="list-file"]').should('be.visible');

  // Continue with the upload process
  cy.NextStep();
  // use advanced mode
  cy.contains('Show Advanced Cognitive Workflow').click();
  // test select category
  cy.SelectAvailableEngine(categoryIdObjectDetection, engineIdMachineboxTagbox);
  cy.NextStep();
  // next step content template
  cy.NextStep();
  // select folder e2e/upload
  cy.SelectFolder();
  // add tags upload
  cy.AddTagsUpload();
  // save upload file
  cy.NextStep();
  cy.contains('created successfully');
});

Cypress.Commands.add('DeleteSelectedFileInUploadScreen', () => {
  // First upload multiple files to have something to delete
  cy.OpenModalUploadFile();

  // Upload first file
  const fileName1 = 'image-plant-jpeg.jpeg';
  const filePath1 = '../setup/image-plant-jpeg.jpeg';
  const mimeType1 = 'image/jpeg';

  cy.SelectFile(fileName1, filePath1, mimeType1);
  cy.ClickToUpload();

  // Wait for the file list to appear
  cy.get('[data-testid="list-file"]', { timeout: 15000 }).should('be.visible');

  // Add a second file using the + icon
  cy.get('[data-testid="add-file"]').should('be.visible').click();

  // Upload second file
  const fileName2 = 'e2e_audio.mp3';
  const filePath2 = '../setup/e2e_audio.mp3';
  const mimeType2 = 'audio/mp3';

  cy.SelectFile(fileName2, filePath2, mimeType2);
  cy.ClickToUpload();

  // Now we should have 2 files - select one to delete
  cy.get('[data-testid="checkbox-file"]').first().should('be.visible').click();

  // Verify the delete button is now enabled and click it
  cy.get('[data-testid="remove-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  // Verify that we still have the file list (with remaining files)
  cy.get('[data-testid="list-file"]').should('be.visible');

  // Verify the delete functionality worked by checking that we can still proceed
  // Just verify we can continue to the next step without completing the full flow
  cy.NextStep();

  // Verify we reached the processing step
  cy.contains('Processing').should('be.visible');
});

Cypress.Commands.add('UploadFileWithoutEngines', () => {
  // Upload a file using the basic flow but skip engine selection
  cy.OpenModalUploadFile();

  const fileName = 'image-plant-jpeg.jpeg';
  const filePath = '../setup/image-plant-jpeg.jpeg';
  const mimeType = 'image/jpeg';

  cy.SelectFile(fileName, filePath, mimeType);
  cy.ClickToUpload();

  // Edit the file
  cy.EditFile(fileName);

  // Go to next step (Processing)
  cy.NextStep();

  // We should be in Simple Cognitive Workflow mode by default
  cy.contains('Simple Cognitive Workflow').should('be.visible');

  // Switch to advanced mode to have more control over the flow
  cy.contains('Show Advanced Cognitive Workflow').click();

  // Now we should be in advanced mode where we can skip engine selection
  cy.contains('Advanced Cognitive Workflow').should('be.visible');

  // Skip engine selection by going directly to next step
  cy.NextStep();

  // Next step content template
  cy.NextStep();

  // Select folder e2e/upload
  cy.SelectFolder();

  // Add tags upload
  cy.AddTagsUpload();

  // Save upload file without any engines
  cy.NextStep();

  // Verify successful upload without engines
  cy.contains('created successfully');
});

Cypress.Commands.add(
  'UploadAudioFileWithTranslationRequiringTranscription',
  () => {
    // Upload an audio file
    cy.OpenModalUploadFile();

    const fileName = 'e2e_audio.mp3';
    const filePath = '../setup/e2e_audio.mp3';
    const mimeType = 'audio/mp3';

    cy.SelectFile(fileName, filePath, mimeType);
    cy.ClickToUpload();

    // Edit the file
    cy.EditFile(fileName);

    // Go to next step (Processing)
    cy.NextStep();

    // Switch to advanced mode for better control
    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.contains('Advanced Cognitive Workflow').should('be.visible');

    // Select ONLY Translation engine (without Transcription)
    // This should cause an error because audio files need transcription first
    cy.log(
      'Selecting ONLY Translation engine for audio file (should cause error)'
    );
    cy.SelectAvailableEngine(categoryIdTranslate, engineIdAmazonTranslate);

    // Try to continue with the upload process
    cy.NextStep();

    // Next step content template
    cy.NextStep();

    // Select folder e2e/upload
    cy.SelectFolder();

    // Add tags upload
    cy.AddTagsUpload();

    // Try to save upload file - this should trigger the error
    cy.NextStep();

    // Take a screenshot to see what's happening
    cy.screenshot('after-trying-to-save-translation-only');

    // Check for error messages or successful completion
    cy.get('body', { timeout: 20000 }).then(($body) => {
      const bodyText = $body.text();

      // Look for error messages
      if (bodyText.includes('error') || bodyText.includes('Error')) {
        cy.log('Found error message on page');

        // Check for transcription-related errors
        if (
          bodyText.includes('transcription') ||
          bodyText.includes('Transcription')
        ) {
          cy.log('Found transcription-related error message');
        }

        // Check for translation-related errors
        if (bodyText.includes('translate') || bodyText.includes('Translate')) {
          cy.log('Found translation-related error message');
        }
      } else if (bodyText.includes('created successfully')) {
        // System handled dependency automatically
        cy.log(
          'Upload completed successfully - system handled transcription dependency'
        );
      } else {
        cy.log('Checking current page state for debugging');
      }

      return null;
    });

    // Log completion for debugging
    cy.log(
      'Test completed - checked for translation/transcription dependency handling'
    );
  }
);

Cypress.Commands.add('EditMediaFileBeforeCompletingUpload', () => {
  // Upload a file
  cy.OpenModalUploadFile();

  const fileName = 'image-plant-jpeg.jpeg';
  const filePath = '../setup/image-plant-jpeg.jpeg';
  const mimeType = 'image/jpeg';

  cy.SelectFile(fileName, filePath, mimeType);
  cy.ClickToUpload();

  // Now we should be on the file list screen
  // Select the file by clicking its checkbox
  cy.get('[data-testid="checkbox-file"]').should('be.visible').click();

  // Click the edit button (pencil icon)
  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  // Verify the edit dialog opens
  cy.contains('Edit Media').should('be.visible');

  // Edit the file information
  const newFileName = 'edited-media-file-before-upload';
  const newDescription = 'This file was edited before upload';

  // Edit file name
  cy.get('[data-test="file-name-upload"] input').should('be.visible');
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type(newFileName);

  // Edit description if available
  cy.get('body').then(($body) => {
    if ($body.find('[data-test="file-description-upload"]').length > 0) {
      cy.get('[data-test="file-description-upload"] textarea').clear();
      cy.get('[data-test="file-description-upload"] textarea').type(
        newDescription
      );
    }
    return null;
  });

  // Add tags
  cy.get('[data-testid="tags-edit"]').click();
  cy.AddTagsUpload();

  // Select category
  cy.contains('General').click();

  // Save the edit
  cy.get('[data-test="save-edit-file"]').click();

  // Verify we're back to the file list
  cy.get('[data-testid="list-file"]').should('be.visible');

  // Continue with the upload process
  cy.NextStep();

  // Use advanced mode for better control
  cy.contains('Show Advanced Cognitive Workflow').click();

  // Select an engine
  cy.SelectAvailableEngine(categoryIdObjectDetection, engineIdMachineboxTagbox);

  cy.NextStep();

  // Next step content template
  cy.NextStep();

  // Select folder e2e/upload
  cy.SelectFolder();

  // Add tags upload
  cy.AddTagsUpload();

  // Save upload file
  cy.NextStep();

  // Verify successful upload with edited information
  cy.contains('created successfully');

  // Log success for debugging
  cy.log('Successfully edited media file before completing upload');
});

Cypress.Commands.add('AddTagsInEditMediaBeforeUpload', () => {
  // Upload a file
  cy.OpenModalUploadFile();

  const fileName = 'image-plant-jpeg.jpeg';
  const filePath = '../setup/image-plant-jpeg.jpeg';
  const mimeType = 'image/jpeg';

  cy.SelectFile(fileName, filePath, mimeType);
  cy.ClickToUpload();

  // Now we should be on the file list screen
  // Select the file by clicking its checkbox
  cy.get('[data-testid="checkbox-file"]').should('be.visible').click();

  // Click the edit button (pencil icon)
  cy.get('[data-test="edit-file"]')
    .should('be.visible')
    .should('not.be.disabled')
    .click();

  // Verify the edit dialog opens
  cy.contains('Edit Media').should('be.visible');

  // Focus specifically on adding tags
  cy.log('Adding tags to media file before upload');

  // Click on the tags editor
  cy.get('[data-testid="tags-edit"]').should('be.visible').click();

  // Add multiple tags to test the functionality
  const tagsToAdd = ['test-tag-1', 'media-edit-tag', 'before-upload-tag'];

  tagsToAdd.forEach((tag, index) => {
    cy.log(`Adding tag ${index + 1}: ${tag}`);

    // Type the tag using the correct selector for edit mode
    cy.get('[data-testid="input-tags"]').type(tag);

    // Click the "Add Tag" button instead of pressing Enter
    cy.get('[data-testid="add-tag"]').click();

    // Verify the tag was added as a chip
    cy.get('[data-testid="chip"]').contains(tag).should('be.visible');
  });

  // Verify all tags are visible
  cy.log('Verifying all tags were added successfully');
  tagsToAdd.forEach((tag) => {
    cy.get('[data-testid="chip"]').contains(tag).should('be.visible');
  });

  // Switch back to General tab to save
  cy.get('[data-testid="general-edit"]').click();

  // Select category
  cy.contains('General').click();

  // Save the edit
  cy.get('[data-test="save-edit-file"]').click();

  // Verify we're back to the file list
  cy.get('[data-testid="list-file"]').should('be.visible');

  // Continue with the upload process
  cy.NextStep();

  // Use advanced mode for better control
  cy.contains('Show Advanced Cognitive Workflow').click();

  // Select an engine
  cy.SelectAvailableEngine(categoryIdObjectDetection, engineIdMachineboxTagbox);

  cy.NextStep();

  // Next step content template
  cy.NextStep();

  // Select folder e2e/upload
  cy.SelectFolder();

  // Add tags upload (this will add additional tags during the upload process)
  cy.AddTagsUpload();

  // Save upload file
  cy.NextStep();

  // Verify successful upload with tags
  cy.contains('created successfully');

  // Log success for debugging
  cy.log('Successfully added tags in edit media before completing upload');
});
