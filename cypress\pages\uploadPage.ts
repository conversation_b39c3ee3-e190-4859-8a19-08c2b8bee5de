import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTextExtraction,
  engineIdTextExtraction,
  categoryIdTranscription,
  engineIdTranscription,
  categoryIdSpeakerDetection,
  engineIdSpeakerSeparation,
  categoryIdTranslate,
  engineIdTranslateSpanishToEnglish,
  veritoneAppId,
} from '../fixtures/variables';
import { deleteTDOByFilePath } from '../../src/state/modules/tdo/index';

export const uploadPage = {
  deleteFileByName: (nameToDelete: string): void => {
    // clean up all the file in that folder before upload test
    const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
    const token = Cypress.env('token');
    cy.wrap(deleteTDOByFilePath(endpoint, token, veritoneAppId, nameToDelete));
  },

  navigateToUploadFolderAndDeleteFile: (): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-pdf.pdf'
        )
      );
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-email.eml'
        )
      );
      return;
    });

    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  uploadImage: (): void => {
    const imageName = 'image-plant-jpeg.jpeg';
    const imagePath = '../setup/image-plant-jpeg.jpeg';
    const mimeTypeImage = 'image/png';
    cy.UploadFileBasic(
      imageName,
      imagePath,
      mimeTypeImage,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadPDFFile: (): void => {
    const pdfName = 'spanish-pdf.pdf';
    const pdfPath = '../setup/spanish-pdf.pdf';
    const mimeTypePdf = 'application/pdf';
    cy.UploadFileBasic(
      pdfName,
      pdfPath,
      mimeTypePdf,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadEMLFile: (): void => {
    const emlName = 'spanish-email.eml';
    const emlPath = '../setup/spanish-email.eml';
    const mimeTypeEml = 'message/rfc822';
    cy.UploadFileBasic(
      emlName,
      emlPath,
      mimeTypeEml,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadVideoFile: (): void => {
    const fileName = 'bloomberg.mp4';
    const filePath = '../setup/bloomberg.mp4';
    const mimeType = 'video/mp4';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  uploadAudioFile: (): void => {
    const fileName = 'e2e_audio.mp3';
    const filePath = '../setup/e2e_audio.mp3';
    const mimeType = 'audio/mp3';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  UploadTextFile: (): void => {
    const textName = 'spanish-txt.txt';
    const textPath = '../setup/spanish-txt.txt';
    const mimeTypeText = 'text/plain';
    cy.UploadFileBasic(
      textName,
      textPath,
      mimeTypeText,
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish
    );
  },
  clickUploadButton(): void {
    cy.get('[data-test="upload-file"]').click();
  },
  verifyInitialUploadScreen(): void {
    cy.get('[role="dialog"]').within(() => {
      cy.contains('h6', 'Upload').should('be.visible');

      cy.get('[data-testid="stepper"]').within(() => {
        cy.contains('span', 'File Upload').should('have.class', 'Mui-active');
        cy.contains('span', 'Processing').should('have.class', 'Mui-disabled');
      });

      cy.get('[data-testid="cloud-upload"]').should('be.visible');
      cy.contains('div', 'Upload Media').should('be.visible');
      cy.contains(
        'div',
        'Select Video, Audio, Image, or Text files to upload'
      ).should('be.visible');
      cy.contains('span', 'Recommended file formats:')
        .parent()
        .should('contain.text', '.mp4, .mp3, .jpg, and .png');

      cy.get('[data-testid="back-button"]').should('be.disabled');
      cy.get('[data-testid="next-button"]').should('be.disabled');
    });
  },
  uploadMultipleFiles(): void {
    const files = [
      {
        fileName: 'image-plant-jpeg.jpeg',
        filePath: '../setup/image-plant-jpeg.jpeg',
        mimeType: 'image/jpeg',
        categoryId: categoryIdObjectDetection,
        engineId: engineIdMachineboxTagbox,
      },
      {
        fileName: 'spanish-txt.txt',
        filePath: '../setup/spanish-txt.txt',
        mimeType: 'text/plain',
        categoryId: categoryIdTranslate,
        engineId: engineIdTranslateSpanishToEnglish,
      },
    ];

    cy.UploadMultipleFilesBasic(files);
  },
  uploadFileBrowse(): void {
    const fileName = 'image-plant-jpeg.jpeg';
    const filePath = '../setup/image-plant-jpeg.jpeg';
    const mimeType = 'image/jpeg';
    cy.UploadFileBasic(
      fileName,
      filePath,
      mimeType,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadImageByUrl(): void {
    const imageUrl = 'https://picsum.photos/300/200.jpg';
    cy.UploadFileByUrl(
      imageUrl,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  addMoreFilesUsingPlusIcon(): void {
    cy.AddMoreFilesWithPlusIcon();
  },
  editSelectedFileInUploadScreen(): void {
    cy.EditSelectedFileInUploadScreen();
  },
  deleteSelectedFileInUploadScreen(): void {
    cy.DeleteSelectedFileInUploadScreen();
  },
  uploadFileWithoutEngines(): void {
    cy.UploadFileWithoutEngines();
  },
  uploadAudioFileWithTranslationRequiringTranscription(): void {
    cy.UploadAudioFileWithTranslationRequiringTranscription();
  },
  editMediaFileBeforeCompletingUpload(): void {
    cy.EditMediaFileBeforeCompletingUpload();
  },
  addTagsInEditMediaBeforeUpload(): void {
    cy.AddTagsInEditMediaBeforeUpload();
  },
  closeUploadScreen(): void {
    // Step 1: Click upload button to open upload screen
    cy.get('[data-test="upload-file"]').click();
    cy.get('[data-test="upload-media"]').click();

    // Step 2: Immediately try to close the dialog
    // Use force click to bypass any overlay issues
    cy.get('button[aria-label="close upload"]', { timeout: 10000 }).click({
      force: true,
    });

    // Verify the upload screen is closed by checking that the dialog is gone
    cy.get('[role="dialog"]').should('not.exist');
  },
  // TODO: Implement proper drag and drop upload method
  // uploadFileDragAndDrop(): void {
  //   // Implementation needed for actual drag and drop functionality
  //   // Should use real drag and drop events instead of attachFile
  // },
};
